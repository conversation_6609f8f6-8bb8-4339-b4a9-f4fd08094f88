Value Filldown,Required TABLE_TYPE (UnderSize|Jabbers)
Value Interface (\S+)
Value UnderSize (\d+)
Value OverSize (\d+)
Value Collisions (\d+)
Value Fragments (\d+)
Value Jabbers (\d+)
Value CRC_Align_Err (\d+)
Value Align_Err (\d+)
Value FCS_Err (\d+)

Start
  ^Interface\s+(${TABLE_TYPE})\s+.* -> TABLE1

TABLE1
  ^--------------- -> 
  # 解析第一个表格格式的数据行
  ^${Interface}\s+${UnderSize}\s+${OverSize}\s+${Collisions}\s+${Fragments}\s*$$ -> Record
  ^Interface\s+(${TABLE_TYPE})\s+.* -> TABLE2

TABLE2
  # 忽略分隔符行
  ^--------------- ->
  ^${Interface}\s+${Jabbers}\s+${CRC_Align_Err}\s+${<PERSON><PERSON>_Err}\s+${FCS_Err}\s*$$ -> Record
  # 遇到命令提示符，结束解析
  ^.*#\s*$$ -> EOF